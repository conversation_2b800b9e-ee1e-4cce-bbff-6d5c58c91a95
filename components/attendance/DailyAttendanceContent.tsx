'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import AttendanceEmployeeList from './AttendanceEmployeeList';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import Link from 'next/link';
import DashboardCard from '@/components/ui/DashboardCard';
import { Pie } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';

// Register the required chart components
ChartJS.register(ArcElement, Tooltip, Legend);

interface Employee {
  employee_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string | null;
  phone_number: string | null;
  position: string | null;
  department_id: string | null;
  department_name: string | null;
  status: string;
  attendance: {
    status: string;
    attendance_id?: string;
    check_in_time?: string | null;
    check_out_time?: string | null;
    total_hours?: number | null;
    reason?: string;
  };
}

interface DailyAttendanceData {
  metadata: {
    date: string;
    total_employees: number;
  };
  summary: {
    present_count: number;
    absent_count: number;
    on_leave_count: number;
    attendance_percentage: number;
  };
  department_summary: Array<{
    department_id: string;
    department_name: string;
    present_count: number;
    absent_count: number;
    on_leave_count: number;
    total_employees: number;
    attendance_percentage: number;
  }>;
  present_employees: {
    count: number;
    data: Employee[];
  };
  absent_employees: {
    count: number;
    data: Employee[];
  };
  on_leave_employees: {
    count: number;
    data: Employee[];
  };
  status: string;
}

const DailyAttendanceContent: React.FC = () => {
  const { companies } = useAuth();
  const [attendanceData, setAttendanceData] = useState<DailyAttendanceData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());

  useEffect(() => {
    fetchDailyAttendance();
  }, [companies, selectedDate]);

  const fetchDailyAttendance = async () => {
    try {
      setIsLoading(true);
      setError('');

      if (!companies || companies.length === 0) {
        throw new Error('No company found');
      }

      const companyId = companies[0].company_id;
      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      // Format the date as YYYY-MM-DD
      const formattedDate = selectedDate.toISOString().split('T')[0];

      // Determine if we're fetching today's data or a specific date
      const isToday = new Date().toISOString().split('T')[0] === formattedDate;
      const endpoint = isToday
        ? `api/attendance/daily?company_id=${companyId}`
        : `api/attendance/daily?company_id=${companyId}&date=${formattedDate}`;

      const response = await apiGet<DailyAttendanceData>(
        endpoint,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      setAttendanceData(response);
    } catch (error: any) {
      setError(error.message || 'Failed to fetch attendance data');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle date change
  const handleDateChange = (date: Date | null) => {
    if (date) {
      setSelectedDate(date);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-secondary-dark">Daily Attendance</h1>
        <div className="flex items-center space-x-4">
          <DatePicker
            selected={selectedDate}
            onChange={handleDateChange}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            dateFormat="yyyy-MM-dd"
            maxDate={new Date()}
            popperClassName="z-50"
            popperPlacement="bottom-start"
          />
          <Link
            href="/dashboard/hr/attendance/dashboard"
            className="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span>Statistics</span>
          </Link>
          <Link
            href="/dashboard/hr/attendance"
            className="inline-flex items-center justify-center px-6 py-3 text-sm font-medium text-white bg-primary rounded-lg hover:bg-primary-dark hover:text-white focus:outline-none focus:ring-2 focus:ring-primary whitespace-nowrap transition-colors"
            style={{ color: 'white' }}
          >
            <span className="text-white">View All Attendance</span>
          </Link>
        </div>
      </div>

      {/* Breadcrumbs */}
      <div className="text-sm text-secondary">
        <Link href="/dashboard/hr" className="hover:text-primary">Dashboard</Link>
        <span className="mx-2">/</span>
        <Link href="/dashboard/hr/attendance" className="hover:text-primary">Attendance</Link>
        <span className="mx-2">/</span>
        <span className="text-secondary-dark">Daily Report</span>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
          {error}
        </div>
      )}

      {isLoading ? (
        <div className="py-8 text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <p className="mt-2 text-secondary">Loading attendance data...</p>
        </div>
      ) : attendanceData ? (
        <>
          {/* Pass the attendance data directly to avoid duplicate API calls */}
          <DashboardCard title="Daily Attendance">
            <div className="mb-4 flex justify-between items-center">
              <div className="text-sm text-secondary">
                <span>
                  Date: {attendanceData.metadata.date} |
                  Total Employees: {attendanceData.metadata.total_employees}
                </span>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <h3 className="text-lg font-medium text-secondary-dark mb-4">Attendance Overview</h3>
                  <div className="h-64">
                    {attendanceData && (
                      <Pie
                        data={{
                          labels: ['Present', 'Absent', 'On Leave'],
                          datasets: [
                            {
                              data: [
                                attendanceData.summary.present_count,
                                attendanceData.summary.absent_count,
                                attendanceData.summary.on_leave_count
                              ],
                              backgroundColor: [
                                'rgba(75, 192, 192, 0.6)',
                                'rgba(255, 99, 132, 0.6)',
                                'rgba(255, 206, 86, 0.6)'
                              ],
                              borderColor: [
                                'rgba(75, 192, 192, 1)',
                                'rgba(255, 99, 132, 1)',
                                'rgba(255, 206, 86, 1)'
                              ],
                              borderWidth: 1,
                            },
                          ],
                        }}
                        options={{
                          responsive: true,
                          plugins: {
                            legend: {
                              position: 'bottom',
                            },
                            tooltip: {
                              callbacks: {
                                label: function(context: any) {
                                  const label = context.label || '';
                                  const value = context.raw || 0;
                                  const total = attendanceData.metadata.total_employees;
                                  const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                  return `${label}: ${value} (${percentage}%)`;
                                }
                              }
                            }
                          },
                        }}
                      />
                    )}
                  </div>
                </div>
              </div>

              <div>
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <h3 className="text-lg font-medium text-secondary-dark mb-4">Attendance Summary</h3>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-green-50 p-4 rounded-lg">
                      <p className="text-sm text-secondary">Present</p>
                      <p className="text-2xl font-bold text-green-600">{attendanceData.summary.present_count}</p>
                      <p className="text-xs text-secondary mt-1">
                        {attendanceData.summary.present_count > 0
                          ? `${attendanceData.summary.attendance_percentage}% of total`
                          : 'No employees present'}
                      </p>
                    </div>

                    <div className="bg-red-50 p-4 rounded-lg">
                      <p className="text-sm text-secondary">Absent</p>
                      <p className="text-2xl font-bold text-red-600">{attendanceData.summary.absent_count}</p>
                      <p className="text-xs text-secondary mt-1">
                        {attendanceData.summary.absent_count > 0
                          ? `${Math.round((attendanceData.summary.absent_count / attendanceData.metadata.total_employees) * 100)}% of total`
                          : 'No employees absent'}
                      </p>
                    </div>

                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <p className="text-sm text-secondary">On Leave</p>
                      <p className="text-2xl font-bold text-yellow-600">{attendanceData.summary.on_leave_count}</p>
                      <p className="text-xs text-secondary mt-1">
                        {attendanceData.summary.on_leave_count > 0
                          ? `${Math.round((attendanceData.summary.on_leave_count / attendanceData.metadata.total_employees) * 100)}% of total`
                          : 'No employees on leave'}
                      </p>
                    </div>

                    <div className="bg-blue-50 p-4 rounded-lg">
                      <p className="text-sm text-secondary">Total</p>
                      <p className="text-2xl font-bold text-blue-600">{attendanceData.metadata.total_employees}</p>
                      <p className="text-xs text-secondary mt-1">
                        Employees in the company
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Department Summary */}
            {attendanceData.department_summary.length > 0 && (
              <div className="mt-6">
                <h3 className="text-lg font-medium text-secondary-dark mb-4">Department Summary</h3>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                          Department
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                          Present
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                          Absent
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                          On Leave
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                          Total
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                          Attendance %
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {attendanceData.department_summary.map((dept, index) => (
                        <tr key={index} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-secondary-dark">
                              {dept.department_name}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-green-600 font-medium">
                              {dept.present_count}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-red-600 font-medium">
                              {dept.absent_count}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-yellow-600 font-medium">
                              {dept.on_leave_count}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-secondary-dark">
                              {dept.total_employees}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-secondary-dark">
                              {dept.attendance_percentage}%
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </DashboardCard>

          <AttendanceEmployeeList
            presentEmployees={attendanceData.present_employees.data}
            absentEmployees={attendanceData.absent_employees.data}
            onLeaveEmployees={attendanceData.on_leave_employees.data}
            date={attendanceData.metadata.date}
          />
        </>
      ) : (
        <div className="py-8 text-center">
          <p className="text-secondary">No attendance data available for the selected date.</p>
        </div>
      )}
    </div>
  );
};

export default DailyAttendanceContent;
